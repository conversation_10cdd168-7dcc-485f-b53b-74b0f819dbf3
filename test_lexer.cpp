/**
 * @file test_lexer.cpp
 * @brief Simple test for the NEX lexer
 */

#include "nex/lexer/tokenizer.h"
#include <iostream>
#include <fstream>

int main() {
    std::cout << "NEX Lexer Test\n";
    std::cout << "==============\n\n";

    // Test NEX code with import statements
    std::string test_code = R"(
import "standard.nex"
import "6502/6502.nex"
import "math/arithmetic.nex"

template custom_template(var1, var2) address {
    lda var1
    cmp var2
    beq address
}

start:
    clear_registers()
    load_immediate(A, #$05)
    store_register(A, counter)
    branch_if_equal(counter, #$10) done

done:
    brk

counter: .byte $00
)";

    try {
        nex::lexer::Tokenizer tokenizer(test_code, "test.nex");

        std::cout << "Tokenizing test code...\n\n";

        auto tokens = tokenizer.tokenizeAll();

        std::cout << "Found " << tokens.size() << " tokens:\n\n";

        for (const auto& token : tokens) {
            std::cout << token.toString() << "\n";
        }

        // Check for errors
        const auto& errors = tokenizer.getErrors();
        if (!errors.empty()) {
            std::cout << "\nErrors found:\n";
            for (const auto& error : errors) {
                std::cout << "  " << error << "\n";
            }
        } else {
            std::cout << "\nNo lexical errors found!\n";
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}