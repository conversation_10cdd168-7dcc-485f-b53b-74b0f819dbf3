/**
 * @file test_lexer.cpp
 * @brief Simple test for the NEX lexer
 */

#include "nex/lexer/tokenizer.h"
#include <iostream>
#include <fstream>

int main() {
    std::cout << "NEX Lexer Test\n";
    std::cout << "==============\n\n";

    // Test NEX code with new C-like syntax and Pascal casing
    std::string test_code = R"(
// Import standard library
import "standard/6502.nex"

// Variable declarations
u8 myValue;
u16 myAddress;
u8 myArray[10];

// Set reset vector
set reset main;

main($8000) {
    // Initialize system
    ClearRegisters();
    ClearMemory();

    // Assignment operations
    myValue = 5;
    myAddress = &myValue;

    // Array operations with loop
    forx(10) {
        myArray[x] = x;
    }

    // Conditional logic
    BranchIfEqual(myValue, #5, done);

done:
    SaveRegisters();
    RestoreRegisters();
}
)";

    try {
        nex::lexer::Tokenizer tokenizer(test_code, "test.nex");

        std::cout << "Tokenizing test code...\n\n";

        auto tokens = tokenizer.tokenizeAll();

        std::cout << "Found " << tokens.size() << " tokens:\n\n";

        for (const auto& token : tokens) {
            std::cout << token.toString() << "\n";
        }

        // Check for errors
        const auto& errors = tokenizer.getErrors();
        if (!errors.empty()) {
            std::cout << "\nErrors found:\n";
            for (const auto& error : errors) {
                std::cout << "  " << error << "\n";
            }
        } else {
            std::cout << "\nNo lexical errors found!\n";
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}