cmake_minimum_required(VERSION 3.20)

project(NEX
    VERSION 1.0.0
    DESCRIPTION "NEX - Template-based Macro Assembler for 6502 and other CPUs"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic -O2")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic -O2")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
endif()

# Include directories
include_directories(include)

# Source files (will be added as we implement them)
set(NEX_SOURCES
    src/lexer/tokenizer.cpp
    src/lexer/token.cpp
    # src/parser/parser.cpp
    # src/parser/ast_builder.cpp
    # src/templates/template.cpp
    # src/templates/template_registry.cpp
    # src/templates/parameter_matcher.cpp
    # src/cpu/interface/cpu_interface.cpp
    # src/cpu/impl/cpu_6502.cpp
    # src/codegen/code_generator.cpp
    # src/codegen/optimizer.cpp
    # src/ast/ast_node.cpp
    # src/ast/template_nodes.cpp
    # src/ast/instruction_nodes.cpp
    # src/utils/error_handler.cpp
    # src/utils/string_utils.cpp
)

# Header files (will be added as we implement them)
set(NEX_HEADERS
    include/nex/lexer/tokenizer.h
    include/nex/lexer/token.h
    # include/nex/parser/parser.h
    # include/nex/parser/ast_builder.h
    # include/nex/templates/template.h
    # include/nex/templates/template_registry.h
    # include/nex/templates/parameter_matcher.h
    # include/nex/cpu/cpu_interface.h
    # include/nex/cpu/cpu_6502.h
    # include/nex/codegen/code_generator.h
    # include/nex/codegen/optimizer.h
    # include/nex/ast/ast_node.h
    # include/nex/ast/template_nodes.h
    # include/nex/ast/instruction_nodes.h
    # include/nex/utils/error_handler.h
    # include/nex/utils/string_utils.h
)

# Create NEX library (only if we have sources)
if(NEX_SOURCES)
    add_library(nex_lib STATIC ${NEX_SOURCES} ${NEX_HEADERS})
    target_include_directories(nex_lib PUBLIC include)
    set(NEX_LIB_TARGET nex_lib)
else()
    # Create a dummy library for now
    add_library(nex_lib INTERFACE)
    target_include_directories(nex_lib INTERFACE include)
    set(NEX_LIB_TARGET nex_lib)
endif()

# Create NEX compiler executable
add_executable(nex src/main.cpp)
target_link_libraries(nex ${NEX_LIB_TARGET})

# Enable testing
enable_testing()

# Find Google Test
find_package(GTest QUIET)
if(GTest_FOUND)
    # Unit tests
    file(GLOB_RECURSE TEST_SOURCES tests/unit/*.cpp)
    add_executable(nex_tests ${TEST_SOURCES})
    target_link_libraries(nex_tests ${NEX_LIB_TARGET} GTest::gtest GTest::gtest_main)

    # Add test discovery
    include(GoogleTest)
    gtest_discover_tests(nex_tests)
else()
    message(WARNING "Google Test not found. Tests will not be built.")
endif()

# Installation
install(TARGETS nex DESTINATION bin)
install(TARGETS ${NEX_LIB_TARGET} DESTINATION lib)
install(DIRECTORY include/nex DESTINATION include)

# Package configuration
set(CPACK_PACKAGE_NAME "NEX")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION "${PROJECT_DESCRIPTION}")
include(CPack)