# NEX - Template-based Macro Assembler

NEX is a modern C++ compiler for a revolutionary programming language that bridges the gap between assembly and high-level programming. Designed primarily for 6502 CPUs with extensibility to other retro architectures like the Z80, NEX introduces powerful template metaprogramming capabilities that transform traditional assembly programming.

## 🚀 Key Features

- **🔧 Template Metaprogramming**: Define reusable code templates with parameters and default values
- **🎯 Template Overloading**: Multiple template definitions with different parameter signatures
- **🏗️ CPU Extensibility**: Abstract CPU interface for easy addition of new architectures
- **⚡ Compile-time Optimization**: Zero runtime overhead from template expansion
- **🛡️ Modern C++**: Built with C++20 standards for performance, safety, and maintainability
- **📝 Rich Syntax**: Familiar assembly syntax enhanced with modern programming constructs
- **🔍 Advanced Lexical Analysis**: Comprehensive tokenization with detailed error reporting

## 💡 The NEX Language

NEX combines the power of assembly language with modern C-like syntax and template metaprogramming. It features Pascal casing for function calls and a familiar programming structure while maintaining zero runtime overhead.

### Modern C-like Syntax

```nex
// C-style comments
import "standard/6502.nex"

// Variable declarations with types
u8  myValue;
u16 myAddress;
u8  myArray[10];

// Address specification
u8($20) myData;

// Function-like main structure
main($8000) {
    // Pascal casing for function calls
    ClearRegisters();
    ClearMemory();

    // Assignment operations
    myValue = 5;
    myAddress = &myData;  // Address operator

    // Array operations with loops
    forx(10) {
        myArray[x] = x;
    }
}
```

### Template Definition Syntax

```nex
template TemplateName(param1, param2, ...) [additional_params] {
    // Assembly instructions using parameters
}
```

### Template Examples with Pascal Casing

```nex
template BranchIfEqual(var1, var2) target {
    lda var1
    cmp var2
    beq target
}

template ClearMemory(address) {
    lda #0
    sta address
}
```

### Usage in Code

```nex
// Pascal casing function calls
BranchIfEqual(counter, #10, done_label);
ClearMemory(myVariable);
LoadImmediate(#5);
```

### Advanced Template Example

```nex
template loop_counter(counter, limit) loop_start, loop_end {
    lda counter
    cmp #limit
    bcs loop_end
    inc counter
    jmp loop_start
}

; Usage
main_loop:
    ; Your loop body here
    loop_counter(my_counter, 10) main_loop, exit_loop

exit_loop:
    ; Continue execution
```

## 📚 Standard Library System

NEX includes a comprehensive standard library system that provides pre-built templates for common programming tasks.

### Import System

Use the `import` statement to include library templates:

```nex
import "standard.nex"           // Core standard library
import "6502/6502.nex"         // 6502-specific optimizations
import "math/arithmetic.nex"   // Extended math operations
```

### Available Libraries

#### Core Libraries
- **`standard.nex`** - Essential templates (branches, loops, arithmetic)
- **`6502/6502.nex`** - 6502-specific optimized templates
- **`math/arithmetic.nex`** - Extended arithmetic (16-bit math, multiplication)

#### Library Categories
- **`standard/`** - Core functionality (memory, control flow, utilities)
- **`6502/`** - 6502-specific optimizations (zero page, addressing modes)
- **`z80/`** - Z80-specific templates (planned)
- **`math/`** - Mathematical operations (arithmetic, bitwise, fixed-point)
- **`utils/`** - General utilities (string manipulation, debugging)
- **`io/`** - Input/Output operations
- **`graphics/`** - Graphics and display templates

### Standard Library Examples

```nex
// Import standard library
import "standard/6502.nex"

// Variable declarations
u8 counter;
u16 address;

main($8000) {
    // Use standard library templates with Pascal casing
    ClearRegisters();                   // Set A, X, Y to 0
    ClearMemory();                      // Clear zero page

    // Assignment operations
    counter = 10;                       // Load immediate and store
    address = &counter;                 // Get address of variable

    // Conditional logic
    BranchIfEqual(counter, #10, equal_ten);

    // Arithmetic operations
    AddImmediate(counter, #5);          // Add 5 to counter
    Increment(counter);                 // Increment by 1

    // Loop constructs with arrays
    forx(20) {
        myArray[x] = x;                 // Array assignment
    }
}
```

### 6502-Specific Optimizations

```nex
import "6502/6502.nex"

start:
    // Zero page optimizations
    zp_copy($80, $81)                   // Fast zero page copy
    zp_clear($82)                       // Clear zero page location

    // Indirect addressing
    load_indirect($80, #5)              // Load via (zp),Y
    store_indirect($80, #5)             // Store via (zp),Y

    // Register operations
    register_swap(A, X)                 // Swap registers efficiently
    push_immediate(#$42)                // Push value to stack
```

## 🛠️ Building and Installation

### Prerequisites

- **CMake 3.20 or higher**
- **C++20 compatible compiler**:
  - GCC 10+ (recommended)
  - Clang 10+
  - MSVC 2019+
- **Optional**: Google Test for unit tests

### Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd NEX

# Build the project
mkdir build
cd build
cmake ..
make

# Test the compiler
./nex --help
```

### Detailed Build Instructions

1. **Configure the build**:
   ```bash
   mkdir build && cd build
   cmake .. [options]
   ```

   Available CMake options:
   - `-DCMAKE_BUILD_TYPE=Release` (Release/Debug)
   - `-DCMAKE_CXX_COMPILER=g++` (specify compiler)

2. **Compile**:
   ```bash
   make -j$(nproc)  # Use all available cores
   ```

3. **Install** (optional):
   ```bash
   sudo make install
   ```

## 🎮 Usage

### Command Line Interface

```bash
nex [options] <input.nex>
```

#### Options

| Option | Description | Default |
|--------|-------------|---------|
| `-o <output>` | Specify output file | `input.asm` |
| `-t <target>` | Target CPU (6502, z80) | `6502` |
| `-O <level>` | Optimization level (0-3) | `2` |
| `-v, --verbose` | Enable verbose output | `false` |
| `-h, --help` | Show help message | - |

#### Examples

```bash
# Basic compilation
./nex program.nex

# Specify output file and target
./nex -o output.asm -t 6502 program.nex

# Verbose compilation with optimization
./nex -v -O 3 -o optimized.asm program.nex
```

### Testing the Lexer

You can test the lexical analysis component:

```bash
# Compile the lexer test
g++ -std=c++20 -I include test_lexer.cpp -L build -lnex_lib -o test_lexer

# Run the test
./test_lexer
```

## 📁 Project Structure

```
NEX/
├── 📂 src/                     # Source implementation
│   ├── 📂 lexer/              # Lexical analysis (tokenization)
│   ├── 📂 parser/             # Syntax analysis (AST generation)
│   ├── 📂 templates/          # Template system core
│   ├── 📂 cpu/                # CPU abstraction layer
│   │   ├── 📂 interface/      # Abstract CPU interface
│   │   └── 📂 impl/           # CPU implementations (6502, Z80)
│   ├── 📂 codegen/            # Code generation backend
│   ├── 📂 ast/                # Abstract Syntax Tree nodes
│   ├── 📂 utils/              # Utility functions
│   └── 📄 main.cpp            # Compiler entry point
├── 📂 include/nex/            # Public header files
│   └── 📂 [mirrors src structure]
├── 📂 lib/                    # 📚 Standard Library System
│   ├── 📂 standard/           # Core standard library
│   │   ├── 📄 standard.nex   # Essential templates
│   │   ├── 📄 memory.nex     # Memory management
│   │   └── 📄 control.nex    # Control flow templates
│   ├── 📂 6502/              # 6502-specific libraries
│   │   ├── 📄 6502.nex       # 6502 optimizations
│   │   ├── 📄 zeropage.nex   # Zero page utilities
│   │   └── 📄 addressing.nex # Addressing modes
│   ├── 📂 z80/               # Z80-specific libraries (planned)
│   ├── 📂 math/              # Mathematical operations
│   │   ├── 📄 arithmetic.nex # Extended arithmetic
│   │   ├── 📄 bitwise.nex    # Bit manipulation
│   │   └── 📄 fixed_point.nex# Fixed-point math
│   ├── 📂 utils/             # General utilities
│   ├── 📂 io/                # Input/Output operations
│   ├── 📂 graphics/          # Graphics templates
│   └── 📄 README.md          # Library documentation
├── 📂 tests/                  # Test suite
│   ├── 📂 unit/              # Unit tests
│   └── 📂 integration/       # Integration tests
├── 📂 examples/               # Example NEX programs
│   ├── 📄 hello_world.nex    # Basic template example
│   └── 📄 library_demo.nex   # Comprehensive library demo
├── 📂 docs/                   # Documentation
├── 📄 CMakeLists.txt         # Build configuration
├── 📄 ARCHITECTURE.md        # Detailed design document
└── 📄 README.md              # This file
```

## 🚧 Development Status

**Current Implementation Status:**

- ✅ **Project Infrastructure**
  - [x] Modern C++ project structure
  - [x] CMake build system
  - [x] Cross-platform compatibility
  - [x] Command-line interface

- ✅ **Lexical Analysis**
  - [x] Complete tokenizer implementation
  - [x] C-like syntax support (// comments, {}, [], etc.)
  - [x] Pascal casing function recognition
  - [x] Type keywords (u8, u16)
  - [x] Modern operators (&, =, [], etc.)
  - [x] Template keyword recognition
  - [x] Import statement support
  - [x] 6502 instruction set tokenization
  - [x] Number literals (decimal, hex, binary)
  - [x] String literals with escape sequences
  - [x] Comprehensive error reporting

- ✅ **Standard Library System**
  - [x] Library organization structure
  - [x] Import syntax (`import "library.nex"`)
  - [x] Pascal casing standard library (`standard/6502.nex`)
  - [x] Core templates (ClearRegisters, ClearMemory, etc.)
  - [x] Array operations (StoreArrayX, LoadArrayX, etc.)
  - [x] Loop constructs (ForX, ForY)
  - [x] 6502-specific optimizations
  - [x] Extended math library (`math/arithmetic.nex`)
  - [x] Comprehensive template documentation
  - [x] Library search path design

- ✅ **Modern C-like Syntax**
  - [x] Variable declarations (u8, u16)
  - [x] Array syntax with brackets []
  - [x] Assignment operators (=)
  - [x] Address operators (&)
  - [x] Function-like main structure
  - [x] C-style comments (//)
  - [x] Pascal casing convention
  - [x] Reset vector syntax (set reset main)

- 🚧 **In Progress**
  - [ ] Parser and AST generation
  - [ ] Template system core
  - [ ] Template parameter matching
  - [ ] Template overload resolution

- 📋 **Planned**
  - [ ] 6502 CPU implementation
  - [ ] Code generation backend
  - [ ] Template expansion engine
  - [ ] Compile-time optimization
  - [ ] Z80 CPU support
  - [ ] Advanced template features

## 🧪 Examples

### Hello World Example

See `examples/hello_world.nex` for a complete example demonstrating:
- Template definitions with parameters
- Template overloading
- 6502 assembly integration
- Memory organization

### Running Examples

```bash
# Compile the hello world example
./nex -v examples/hello_world.nex

# View the generated output
cat hello_world.asm
```

## 🤝 Contributing

We welcome contributions! This project is in active development.

### Getting Started
1. Read [ARCHITECTURE.md](ARCHITECTURE.md) for design details
2. Check the current task list and development status
3. Look for issues labeled "good first issue"
4. Follow the existing code style and patterns

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📚 Documentation

- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Detailed system design and architecture
- **[examples/](examples/)** - Sample NEX programs and use cases
- **Source code** - Extensively commented for learning and contribution

## 🔧 Troubleshooting

### Common Build Issues

1. **C++20 not supported**: Ensure you have a modern compiler (GCC 10+, Clang 10+)
2. **CMake version**: Update to CMake 3.20 or higher
3. **Missing dependencies**: Install build essentials for your platform

### Getting Help

- Check existing issues in the repository
- Review the architecture documentation
- Examine the example programs
- Look at the test files for usage patterns

## 📄 License

This project is currently under development. License terms will be determined upon initial release.

---

**NEX** - *Bringing modern programming concepts to retro computing* 🎮✨
