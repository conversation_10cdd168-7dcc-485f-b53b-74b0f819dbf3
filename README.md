# NEX - Template-based Macro Assembler

NEX is a modern C++ compiler for a new programming language that works as a macro assembler for 6502 CPUs, with extensibility to other CPUs like the Z80. It features powerful template metaprogramming capabilities that allow extending assembly language concepts up to high-level programming constructs.

## Features

- **Template Metaprogramming**: Define reusable code templates with parameters
- **Template Overloading**: Multiple template definitions with different parameter signatures
- **CPU Extensibility**: Abstract CPU interface for easy addition of new architectures
- **Compile-time Optimization**: Zero runtime overhead from template expansion
- **Modern C++**: Built with C++20 standards for performance and safety

## Template System

NEX allows you to define templates that extend basic assembly instructions:

```nex
template boeq(var1, var2) address {
    lda var1
    cmp var2
    beq address
}

template boeq(var1, var2=A) address {
    cmp var1      // assumes var2 is already in A
    beq address
}
```

Usage in code:
```nex
boeq(variable, #5) done_label    // Full template
boeq(variable) done_label        // Uses default parameter
```

## Building

### Prerequisites
- CMake 3.20 or higher
- C++20 compatible compiler (GCC 10+, Clang 10+, MSVC 2019+)
- Optional: Google Test for unit tests

### Build Instructions

```bash
mkdir build
cd build
cmake ..
make
```

### Running

```bash
./nex [options] <input.nex>

Options:
  -o <output>    Specify output file (default: input.asm)
  -t <target>    Target CPU (6502, z80) (default: 6502)
  -O <level>     Optimization level (0-3) (default: 2)
  -v, --verbose  Enable verbose output
  -h, --help     Show help message
```

## Project Structure

```
NEX/
├── src/                    # Source files
│   ├── lexer/             # Lexical analysis
│   ├── parser/            # Syntax analysis
│   ├── templates/         # Template system
│   ├── cpu/               # CPU abstraction layer
│   ├── codegen/           # Code generation
│   ├── ast/               # Abstract Syntax Tree
│   └── utils/             # Utilities
├── include/nex/           # Header files
├── tests/                 # Unit and integration tests
├── examples/              # Example NEX programs
└── docs/                  # Documentation
```

## Development Status

🚧 **Under Active Development** 🚧

Current implementation status:
- [x] Project structure and build system
- [x] Basic CLI interface
- [ ] Lexical analysis
- [ ] Parser and AST
- [ ] Template system core
- [ ] 6502 CPU implementation
- [ ] Code generation
- [ ] Template expansion
- [ ] Optimization passes
- [ ] Z80 CPU support

## Contributing

This project is in early development. See [ARCHITECTURE.md](ARCHITECTURE.md) for detailed design information.

## License

[License to be determined]
