/**
 * @file arithmetic.nex
 * @brief NEX Math Library - Extended Arithmetic Operations
 * @version 1.0.0
 *
 * This library provides extended arithmetic operations beyond basic 6502
 * instructions, including 16-bit math, multiplication, and division.
 */

// =============================================================================
// 16-BIT ARITHMETIC
// =============================================================================

/**
 * @brief Add two 16-bit numbers
 * @param low1 Low byte of first number
 * @param high1 High byte of first number
 * @param low2 Low byte of second number
 * @param high2 High byte of second number
 * @param result_low Low byte of result
 * @param result_high High byte of result
 * @example
 *   add_16bit(num1_lo, num1_hi, num2_lo, num2_hi, sum_lo, sum_hi)
 */
template add_16bit(low1, high1, low2, high2, result_low, result_high) {
    clc
    lda low1
    adc low2
    sta result_low
    lda high1
    adc high2
    sta result_high
}

/**
 * @brief Subtract two 16-bit numbers
 * @param low1 Low byte of first number
 * @param high1 High byte of first number
 * @param low2 Low byte of second number
 * @param high2 High byte of second number
 * @param result_low Low byte of result
 * @param result_high High byte of result
 * @example
 *   subtract_16bit(num1_lo, num1_hi, num2_lo, num2_hi, diff_lo, diff_hi)
 */
template subtract_16bit(low1, high1, low2, high2, result_low, result_high) {
    sec
    lda low1
    sbc low2
    sta result_low
    lda high1
    sbc high2
    sta result_high
}

/**
 * @brief Increment 16-bit number
 * @param low_byte Low byte of number
 * @param high_byte High byte of number
 * @example
 *   increment_16bit(counter_lo, counter_hi)
 */
template increment_16bit(low_byte, high_byte) {
    inc low_byte
    bne skip_high_inc
    inc high_byte
    skip_high_inc:
}

/**
 * @brief Decrement 16-bit number
 * @param low_byte Low byte of number
 * @param high_byte High byte of number
 * @example
 *   decrement_16bit(counter_lo, counter_hi)
 */
template decrement_16bit(low_byte, high_byte) {
    lda low_byte
    bne skip_high_dec
    dec high_byte
    skip_high_dec:
    dec low_byte
}

// =============================================================================
// MULTIPLICATION
// =============================================================================

/**
 * @brief Multiply two 8-bit numbers (result in 16-bit)
 * @param multiplicand First number
 * @param multiplier Second number
 * @param result_low Low byte of result
 * @param result_high High byte of result
 * @example
 *   multiply_8bit(num1, num2, product_lo, product_hi)
 */
template multiply_8bit(multiplicand, multiplier, result_low, result_high) {
    lda #0
    sta result_low
    sta result_high
    ldx #8

    multiply_loop:
        lsr multiplier
        bcc skip_add
        clc
        lda result_low
        adc multiplicand
        sta result_low
        lda result_high
        adc #0
        sta result_high
    skip_add:
        asl multiplicand
        bcc no_overflow
        inc result_high
    no_overflow:
        dex
        bne multiply_loop
}

/**
 * @brief Fast multiply by 2 (left shift)
 * @param value Value to multiply
 * @param result Result location
 * @example
 *   multiply_by_2(number, doubled)
 */
template multiply_by_2(value, result) {
    lda value
    asl
    sta result
}

/**
 * @brief Fast multiply by 4 (left shift twice)
 * @param value Value to multiply
 * @param result Result location
 * @example
 *   multiply_by_4(number, quadrupled)
 */
template multiply_by_4(value, result) {
    lda value
    asl
    asl
    sta result
}

// =============================================================================
// DIVISION
// =============================================================================

/**
 * @brief Divide 8-bit number by 2 (right shift)
 * @param value Value to divide
 * @param result Result location
 * @example
 *   divide_by_2(number, halved)
 */
template divide_by_2(value, result) {
    lda value
    lsr
    sta result
}

/**
 * @brief Divide 8-bit number by 4 (right shift twice)
 * @param value Value to divide
 * @param result Result location
 * @example
 *   divide_by_4(number, quartered)
 */
template divide_by_4(value, result) {
    lda value
    lsr
    lsr
    sta result
}

/**
 * @brief Simple 8-bit division (dividend / divisor)
 * @param dividend Number to divide
 * @param divisor Number to divide by
 * @param quotient Result quotient
 * @param remainder Result remainder
 * @example
 *   divide_8bit(dividend, divisor, quotient, remainder)
 */
template divide_8bit(dividend, divisor, quotient, remainder) {
    lda #0
    sta quotient
    lda dividend
    sta remainder

    divide_loop:
        lda remainder
        cmp divisor
        bcc divide_done
        sec
        sbc divisor
        sta remainder
        inc quotient
        jmp divide_loop
    divide_done:
}