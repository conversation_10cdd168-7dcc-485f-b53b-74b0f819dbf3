/**
 * @file standard.nex
 * @brief NEX Standard Library - Core Templates
 * @version 1.0.0
 *
 * This library provides essential templates that extend basic 6502 assembly
 * functionality with higher-level programming constructs.
 */

// =============================================================================
// COMPARISON AND BRANCHING TEMPLATES
// =============================================================================

/**
 * @brief Branch if two values are equal
 * @param var1 First value to compare
 * @param var2 Second value to compare
 * @param target Branch target label
 * @example
 *   branch_if_equal(counter, #10) loop_end
 */
template branch_if_equal(var1, var2) target {
    lda var1
    cmp var2
    beq target
}

/**
 * @brief Branch if two values are equal (optimized for A register)
 * @param var1 Value to compare (A register assumed to contain second value)
 * @param target Branch target label
 * @example
 *   lda #10
 *   branch_if_equal(counter) loop_end
 */
template branch_if_equal(var1, var2=A) target {
    cmp var1
    beq target
}

/**
 * @brief Branch if two values are not equal
 * @param var1 First value to compare
 * @param var2 Second value to compare
 * @param target Branch target label
 */
template branch_if_not_equal(var1, var2) target {
    lda var1
    cmp var2
    bne target
}

/**
 * @brief Branch if first value is less than second
 * @param var1 First value
 * @param var2 Second value
 * @param target Branch target label
 */
template branch_if_less(var1, var2) target {
    lda var1
    cmp var2
    bcc target
}

/**
 * @brief Branch if first value is greater than or equal to second
 * @param var1 First value
 * @param var2 Second value
 * @param target Branch target label
 */
template branch_if_greater_equal(var1, var2) target {
    lda var1
    cmp var2
    bcs target
}

// =============================================================================
// LOAD AND STORE TEMPLATES
// =============================================================================

/**
 * @brief Load immediate value into register
 * @param register Target register (A, X, Y)
 * @param value Immediate value to load
 * @example
 *   load_immediate(A, #$FF)
 *   load_immediate(X, #10)
 */
template load_immediate(register, value) {
    ld{register} value
}

/**
 * @brief Store register to memory location
 * @param register Source register (A, X, Y)
 * @param location Memory location
 * @example
 *   store_register(A, variable)
 */
template store_register(register, location) {
    st{register} location
}

/**
 * @brief Copy value from one memory location to another
 * @param source Source memory location
 * @param destination Destination memory location
 * @example
 *   copy_memory(source_var, dest_var)
 */
template copy_memory(source, destination) {
    lda source
    sta destination
}

/**
 * @brief Clear memory location (set to zero)
 * @param location Memory location to clear
 * @example
 *   clear_memory(counter)
 */
template clear_memory(location) {
    lda #0
    sta location
}

// =============================================================================
// ARITHMETIC TEMPLATES
// =============================================================================

/**
 * @brief Add immediate value to memory location
 * @param location Memory location
 * @param value Value to add
 * @example
 *   add_immediate(counter, #1)
 */
template add_immediate(location, value) {
    lda location
    clc
    adc value
    sta location
}

/**
 * @brief Add two memory locations and store result
 * @param var1 First operand
 * @param var2 Second operand
 * @param result Result location
 * @example
 *   add_variables(num1, num2, sum)
 */
template add_variables(var1, var2, result) {
    lda var1
    clc
    adc var2
    sta result
}

/**
 * @brief Subtract immediate value from memory location
 * @param location Memory location
 * @param value Value to subtract
 * @example
 *   subtract_immediate(counter, #1)
 */
template subtract_immediate(location, value) {
    lda location
    sec
    sbc value
    sta location
}

/**
 * @brief Increment memory location by 1
 * @param location Memory location to increment
 * @example
 *   increment(counter)
 */
template increment(location) {
    inc location
}

/**
 * @brief Decrement memory location by 1
 * @param location Memory location to decrement
 * @example
 *   decrement(counter)
 */
template decrement(location) {
    dec location
}

// =============================================================================
// LOOP TEMPLATES
// =============================================================================

/**
 * @brief Simple counting loop with counter variable
 * @param counter Counter variable
 * @param limit Loop limit value
 * @param loop_start Loop start label
 * @param loop_end Loop end label
 * @example
 *   loop_start:
 *     ; loop body
 *     counting_loop(my_counter, #10) loop_start, loop_end
 *   loop_end:
 */
template counting_loop(counter, limit) loop_start, loop_end {
    increment(counter)
    branch_if_less(counter, limit) loop_start
}

/**
 * @brief Countdown loop (loop while counter > 0)
 * @param counter Counter variable
 * @param loop_start Loop start label
 * @param loop_end Loop end label
 * @example
 *   loop_start:
 *     ; loop body
 *     countdown_loop(my_counter) loop_start, loop_end
 *   loop_end:
 */
template countdown_loop(counter) loop_start, loop_end {
    decrement(counter)
    branch_if_not_equal(counter, #0) loop_start
}

/**
 * @brief For-loop style template
 * @param counter Counter variable
 * @param start Start value
 * @param end End value
 * @param loop_body Loop body label
 * @param loop_exit Loop exit label
 * @example
 *   for_loop(i, #0, #10) loop_body, loop_exit
 *   loop_body:
 *     ; loop body code
 *     jmp for_loop_continue
 *   loop_exit:
 */
template for_loop(counter, start, end) loop_body, loop_exit {
    load_immediate(A, start)
    store_register(A, counter)
    for_loop_check:
        branch_if_greater_equal(counter, end) loop_exit
        jmp loop_body
    for_loop_continue:
        increment(counter)
        jmp for_loop_check
}

// =============================================================================
// CONDITIONAL TEMPLATES
// =============================================================================

/**
 * @brief If-then conditional structure
 * @param condition_var Variable to test
 * @param test_value Value to compare against
 * @param then_label Label to jump to if condition is true
 * @param else_label Label to jump to if condition is false
 * @example
 *   if_then_else(status, #1) then_block, else_block
 *   then_block:
 *     ; code for true condition
 *     jmp end_if
 *   else_block:
 *     ; code for false condition
 *   end_if:
 */
template if_then_else(condition_var, test_value) then_label, else_label {
    branch_if_equal(condition_var, test_value) then_label
    jmp else_label
}

/**
 * @brief Simple if condition (no else)
 * @param condition_var Variable to test
 * @param test_value Value to compare against
 * @param then_label Label to jump to if condition is true
 * @example
 *   if_condition(flag, #1) do_something
 *   ; continues here if condition is false
 *   do_something:
 *     ; code for true condition
 */
template if_condition(condition_var, test_value) then_label {
    branch_if_equal(condition_var, test_value) then_label
}

// =============================================================================
// UTILITY TEMPLATES
// =============================================================================

/**
 * @brief No operation (delay/placeholder)
 * @param count Number of NOP instructions
 * @example
 *   delay(5)  ; 5 NOP instructions
 */
template delay(count) {
    .repeat count
        nop
    .endrepeat
}

/**
 * @brief Save registers to stack
 * @example
 *   save_registers()
 */
template save_registers() {
    pha  ; Save A
    txa
    pha  ; Save X
    tya
    pha  ; Save Y
}

/**
 * @brief Restore registers from stack
 * @example
 *   restore_registers()
 */
template restore_registers() {
    pla
    tay  ; Restore Y
    pla
    tax  ; Restore X
    pla  ; Restore A
}

/**
 * @brief Swap two memory locations
 * @param var1 First variable
 * @param var2 Second variable
 * @example
 *   swap_variables(temp1, temp2)
 */
template swap_variables(var1, var2) {
    lda var1
    pha
    lda var2
    sta var1
    pla
    sta var2
}