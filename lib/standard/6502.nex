/**
 * @file 6502.nex
 * @brief NEX Standard Library for 6502 CPU - Pascal Casing
 * @version 2.0.0
 *
 * This library provides standard templates using Pascal casing convention
 * for the new C-like syntax of NEX.
 */

// =============================================================================
// SYSTEM INITIALIZATION TEMPLATES
// =============================================================================

/**
 * @brief Clear all CPU registers (A, X, Y)
 * @example
 *   ClearRegisters();
 */
template ClearRegisters() {
    lda #0
    tax
    tay
}

/**
 * @brief Clear memory location or range
 * @example
 *   ClearMemory();           // Clears zero page
 *   ClearMemory(address);    // Clears specific address
 */
template ClearMemory() {
    // Clear zero page temporaries ($00-$0F)
    lda #0
    ldx #$0F
    clear_zp_loop:
        sta $00,x
        dex
        bpl clear_zp_loop
}

template ClearMemory(address) {
    lda #0
    sta address
}

// =============================================================================
// MEMORY OPERATIONS
// =============================================================================

/**
 * @brief Load immediate value into accumulator
 * @param value Immediate value to load
 * @example
 *   LoadImmediate(#5);
 */
template LoadImmediate(value) {
    lda value
}

/**
 * @brief Store accumulator to memory location
 * @param location Memory location
 * @example
 *   StoreAccumulator(variable);
 */
template StoreAccumulator(location) {
    sta location
}

/**
 * @brief Copy value from one memory location to another
 * @param source Source memory location
 * @param destination Destination memory location
 * @example
 *   CopyMemory(source, dest);
 */
template CopyMemory(source, destination) {
    lda source
    sta destination
}

/**
 * @brief Load address into 16-bit variable
 * @param address Address to load
 * @param destination 16-bit destination variable
 * @example
 *   LoadAddress(&myData, pointer);
 */
template LoadAddress(address, destination) {
    lda #<address
    sta destination
    lda #>address
    sta destination+1
}

// =============================================================================
// ARITHMETIC OPERATIONS
// =============================================================================

/**
 * @brief Add immediate value to memory location
 * @param location Memory location
 * @param value Value to add
 * @example
 *   AddImmediate(counter, #1);
 */
template AddImmediate(location, value) {
    lda location
    clc
    adc value
    sta location
}

/**
 * @brief Add two memory locations
 * @param var1 First operand
 * @param var2 Second operand
 * @param result Result location
 * @example
 *   AddVariables(num1, num2, sum);
 */
template AddVariables(var1, var2, result) {
    lda var1
    clc
    adc var2
    sta result
}

/**
 * @brief Increment memory location
 * @param location Memory location to increment
 * @example
 *   Increment(counter);
 */
template Increment(location) {
    inc location
}

/**
 * @brief Decrement memory location
 * @param location Memory location to decrement
 * @example
 *   Decrement(counter);
 */
template Decrement(location) {
    dec location
}

// =============================================================================
// COMPARISON AND BRANCHING
// =============================================================================

/**
 * @brief Branch if two values are equal
 * @param var1 First value to compare
 * @param var2 Second value to compare
 * @param target Branch target label
 * @example
 *   BranchIfEqual(counter, #10, done);
 */
template BranchIfEqual(var1, var2, target) {
    lda var1
    cmp var2
    beq target
}

/**
 * @brief Branch if value is not equal to immediate
 * @param var Variable to compare
 * @param value Immediate value
 * @param target Branch target label
 * @example
 *   BranchIfNotEqual(status, #0, error);
 */
template BranchIfNotEqual(var, value, target) {
    lda var
    cmp value
    bne target
}

/**
 * @brief Branch if first value is less than second
 * @param var1 First value
 * @param var2 Second value
 * @param target Branch target label
 * @example
 *   BranchIfLess(counter, #100, continue);
 */
template BranchIfLess(var1, var2, target) {
    lda var1
    cmp var2
    bcc target
}

// =============================================================================
// ARRAY OPERATIONS
// =============================================================================

/**
 * @brief Store value to array element using X register
 * @param array Base array address
 * @param value Value to store
 * @example
 *   StoreArrayX(myArray, #42);  // myArray[x] = 42
 */
template StoreArrayX(array, value) {
    lda value
    sta array,x
}

/**
 * @brief Load value from array element using X register
 * @param array Base array address
 * @example
 *   LoadArrayX(myArray);  // A = myArray[x]
 */
template LoadArrayX(array) {
    lda array,x
}

/**
 * @brief Store value to array element using Y register
 * @param array Base array address
 * @param value Value to store
 * @example
 *   StoreArrayY(myArray, #42);  // myArray[y] = 42
 */
template StoreArrayY(array, value) {
    lda value
    sta array,y
}

/**
 * @brief Load value from array element using Y register
 * @param array Base array address
 * @example
 *   LoadArrayY(myArray);  // A = myArray[y]
 */
template LoadArrayY(array) {
    lda array,y
}

// =============================================================================
// LOOP CONSTRUCTS
// =============================================================================

/**
 * @brief For loop using X register
 * @param count Number of iterations
 * @example
 *   ForX(10) {
 *       // loop body
 *   }
 */
template ForX(count) {
    ldx #0
    forx_loop_start:
        // Loop body will be inserted here by compiler
        inx
        cpx count
        bcc forx_loop_start
}

/**
 * @brief For loop using Y register
 * @param count Number of iterations
 * @example
 *   ForY(10) {
 *       // loop body
 *   }
 */
template ForY(count) {
    ldy #0
    fory_loop_start:
        // Loop body will be inserted here by compiler
        iny
        cpy count
        bcc fory_loop_start
}

// =============================================================================
// UTILITY TEMPLATES
// =============================================================================

/**
 * @brief Save all registers to stack
 * @example
 *   SaveRegisters();
 */
template SaveRegisters() {
    pha  // Save A
    txa
    pha  // Save X
    tya
    pha  // Save Y
}

/**
 * @brief Restore all registers from stack
 * @example
 *   RestoreRegisters();
 */
template RestoreRegisters() {
    pla
    tay  // Restore Y
    pla
    tax  // Restore X
    pla  // Restore A
}

/**
 * @brief No operation delay
 * @param count Number of NOP instructions
 * @example
 *   Delay(5);
 */
template Delay(count) {
    .repeat count
        nop
    .endrepeat
}