# NEX Standard Libraries

This directory contains the standard library collection for NEX, providing predefined templates and utilities for common programming tasks in assembly language.

## Library Organization

### 📚 Core Libraries

#### `standard/`
Core templates that extend basic assembly functionality:
- **standard.nex** - Essential templates (branches, loops, comparisons)
- **memory.nex** - Memory management templates
- **stack.nex** - Stack operation templates
- **control.nex** - Control flow templates

#### `6502/`
6502-specific optimized templates:
- **6502.nex** - Complete 6502 instruction set templates
- **zeropage.nex** - Zero page optimization templates
- **addressing.nex** - Addressing mode helpers
- **interrupts.nex** - Interrupt handling templates

#### `z80/`
Z80-specific templates (future):
- **z80.nex** - Z80 instruction set templates
- **registers.nex** - Z80 register management
- **ports.nex** - I/O port templates

### 🛠️ Utility Libraries

#### `utils/`
General utility templates:
- **string.nex** - String manipulation templates
- **conversion.nex** - Data conversion templates
- **debug.nex** - Debugging and testing templates

#### `math/`
Mathematical operation templates:
- **arithmetic.nex** - Extended arithmetic operations
- **bitwise.nex** - Bit manipulation templates
- **fixed_point.nex** - Fixed-point math templates

#### `io/`
Input/Output templates:
- **console.nex** - Console I/O templates
- **file.nex** - File operation templates (where applicable)
- **serial.nex** - Serial communication templates

#### `graphics/`
Graphics and display templates:
- **sprites.nex** - Sprite manipulation templates
- **screen.nex** - Screen drawing templates
- **colors.nex** - Color management templates

## Usage

Import libraries using the `import` statement:

```nex
import "standard.nex"           // Core standard library
import "6502/addressing.nex"    // 6502 addressing helpers
import "math/arithmetic.nex"    // Extended math operations
```

## Library Search Path

The compiler searches for libraries in the following order:
1. Current directory
2. `./lib/` (relative to source file)
3. `<NEX_INSTALL>/lib/` (system installation)
4. `NEX_LIB_PATH` environment variable paths

## Creating Custom Libraries

Libraries are regular NEX files containing template definitions:

```nex
// my_library.nex
template my_template(param1, param2) {
    // Template implementation
}

template another_template(value) result {
    // Another template
}
```

## Standard Library Conventions

### Naming Conventions
- **Templates**: `snake_case` (e.g., `branch_if_equal`)
- **Parameters**: `snake_case` (e.g., `source_addr`)
- **Files**: `snake_case.nex` (e.g., `memory_utils.nex`)

### Documentation Format
Each template should include documentation:

```nex
/**
 * @brief Brief description of the template
 * @param param1 Description of parameter 1
 * @param param2 Description of parameter 2
 * @example
 *   branch_if_equal(var1, var2) target_label
 */
template branch_if_equal(var1, var2) target {
    lda var1
    cmp var2
    beq target
}
```

### Parameter Conventions
- **Immediate values**: Prefix with `#` (e.g., `#$FF`)
- **Memory addresses**: Use bare identifiers (e.g., `variable`)
- **Registers**: Use register names (e.g., `A`, `X`, `Y`)
- **Labels**: Use identifiers for jump/branch targets

## Contributing to Standard Libraries

1. Follow naming conventions
2. Include comprehensive documentation
3. Provide usage examples
4. Test with multiple scenarios
5. Consider CPU-specific optimizations
6. Maintain backward compatibility