/**
 * @file 6502.nex
 * @brief NEX 6502-Specific Library
 * @version 1.0.0
 *
 * This library provides 6502-specific optimized templates and utilities
 * that take advantage of the 6502's unique features and addressing modes.
 */

// =============================================================================
// ZERO PAGE OPTIMIZATIONS
// =============================================================================

/**
 * @brief Fast zero page copy
 * @param zp_source Zero page source address
 * @param zp_dest Zero page destination address
 * @example
 *   zp_copy($80, $81)
 */
template zp_copy(zp_source, zp_dest) {
    lda zp_source
    sta zp_dest
}

/**
 * @brief Zero page increment with overflow check
 * @param zp_addr Zero page address
 * @param overflow_label Label to jump to on overflow
 * @example
 *   zp_inc_check($80) overflow_handler
 */
template zp_inc_check(zp_addr) overflow_label {
    inc zp_addr
    beq overflow_label
}

/**
 * @brief Fast zero page clear (set to 0)
 * @param zp_addr Zero page address
 * @example
 *   zp_clear($80)
 */
template zp_clear(zp_addr) {
    lda #0
    sta zp_addr
}

// =============================================================================
// ADDRESSING MODE HELPERS
// =============================================================================

/**
 * @brief Indirect jump through zero page pointer
 * @param zp_pointer Zero page pointer (2 bytes)
 * @example
 *   indirect_jump($80)  ; Jump to address stored at $80-$81
 */
template indirect_jump(zp_pointer) {
    jmp (zp_pointer)
}

/**
 * @brief Load byte via zero page indirect
 * @param zp_pointer Zero page pointer
 * @param offset Y register offset
 * @example
 *   load_indirect($80, #5)  ; Load from (zp_pointer),Y
 */
template load_indirect(zp_pointer, offset) {
    ldy offset
    lda (zp_pointer),y
}

/**
 * @brief Store byte via zero page indirect
 * @param zp_pointer Zero page pointer
 * @param offset Y register offset
 * @param value Value to store (assumes A contains value)
 * @example
 *   lda #$FF
 *   store_indirect($80, #5)  ; Store A to (zp_pointer),Y
 */
template store_indirect(zp_pointer, offset) {
    ldy offset
    sta (zp_pointer),y
}

// =============================================================================
// 6502 REGISTER OPTIMIZATIONS
// =============================================================================

/**
 * @brief Fast register exchange using stack
 * @param reg1 First register (A, X, Y)
 * @param reg2 Second register (A, X, Y)
 * @example
 *   register_swap(A, X)
 */
template register_swap(reg1, reg2) {
    .if reg1 == A && reg2 == X
        pha
        txa
        tax
        pla
    .elseif reg1 == A && reg2 == Y
        pha
        tya
        tay
        pla
    .elseif reg1 == X && reg2 == Y
        txa
        pha
        tya
        tax
        pla
        tay
    .endif
}

/**
 * @brief Set all registers to zero
 * @example
 *   clear_registers()
 */
template clear_registers() {
    lda #0
    tax
    tay
}

/**
 * @brief Load same value into all registers
 * @param value Value to load
 * @example
 *   load_all_registers(#$FF)
 */
template load_all_registers(value) {
    lda value
    tax
    tay
}

// =============================================================================
// STATUS FLAG OPERATIONS
// =============================================================================

/**
 * @brief Branch if carry flag is set
 * @param target Branch target
 * @example
 *   branch_if_carry(error_handler)
 */
template branch_if_carry(target) {
    bcs target
}

/**
 * @brief Branch if carry flag is clear
 * @param target Branch target
 * @example
 *   branch_if_no_carry(continue_processing)
 */
template branch_if_no_carry(target) {
    bcc target
}

/**
 * @brief Test bit and branch
 * @param memory_location Memory location to test
 * @param target Branch target if bit 7 is set
 * @example
 *   test_bit_7($80) negative_handler
 */
template test_bit_7(memory_location) target {
    bit memory_location
    bmi target
}

/**
 * @brief Test bit 6 and branch
 * @param memory_location Memory location to test
 * @param target Branch target if bit 6 is set
 * @example
 *   test_bit_6($80) overflow_handler
 */
template test_bit_6(memory_location) target {
    bit memory_location
    bvs target
}

// =============================================================================
// STACK OPERATIONS
// =============================================================================

/**
 * @brief Push immediate value to stack
 * @param value Immediate value to push
 * @example
 *   push_immediate(#$42)
 */
template push_immediate(value) {
    lda value
    pha
}

/**
 * @brief Push memory location to stack
 * @param location Memory location to push
 * @example
 *   push_memory(variable)
 */
template push_memory(location) {
    lda location
    pha
}

/**
 * @brief Pop from stack to memory location
 * @param location Memory location to store popped value
 * @example
 *   pop_to_memory(variable)
 */
template pop_to_memory(location) {
    pla
    sta location
}