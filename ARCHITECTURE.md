# NEX Compiler Architecture

## Overview

The NEX compiler is a template-based macro assembler designed for 6502 CPUs with extensibility to other architectures like Z80. It combines assembly language with powerful metaprogramming capabilities through a template system.

## Core Design Principles

1. **Template Metaprogramming**: Compile-time template expansion with zero runtime overhead
2. **CPU Extensibility**: Abstract CPU interface allowing easy addition of new architectures
3. **Modern C++**: Leverage C++20/23 features for type safety and performance
4. **Overload Resolution**: Support template variations with different parameter signatures
5. **Compile-time Optimization**: Aggressive optimization during template expansion

## Architecture Components

### 1. Lexical Analysis Layer (`lexer/`)
- **Tokenizer**: Converts source text into tokens
- **Token Types**: Keywords, identifiers, literals, operators, delimiters
- **Template Syntax**: Special handling for template definitions and instantiations

### 2. Syntax Analysis Layer (`parser/`)
- **Parser**: Builds Abstract Syntax Tree (AST) from tokens
- **AST Nodes**: Template definitions, instantiations, assembly instructions
- **Error Recovery**: Robust error handling and reporting

### 3. Template System (`templates/`)
- **Template Registry**: Storage and lookup of template definitions
- **Parameter Matching**: Type-aware parameter resolution
- **Overload Resolution**: Select best template match based on parameters
- **Expansion Engine**: Compile-time template instantiation

### 4. CPU Abstraction Layer (`cpu/`)
- **CPU Interface**: Abstract base class for CPU architectures
- **Instruction Set**: CPU-specific instruction definitions
- **Register Model**: CPU register abstractions
- **Addressing Modes**: CPU-specific addressing mode support

### 5. Code Generation (`codegen/`)
- **Code Generator**: Converts AST to target assembly
- **Optimization**: Compile-time optimizations
- **Symbol Resolution**: Handle labels and addresses
- **Output Formatting**: Generate final assembly output

### 6. CPU Implementations (`cpu/impl/`)
- **6502 Implementation**: Complete 6502 instruction set
- **Z80 Implementation**: Z80 instruction set (future)
- **Extension Framework**: Easy addition of new CPUs

## Key Classes and Interfaces

```cpp
// Core template system
class Template {
    std::string name;
    std::vector<Parameter> parameters;
    std::vector<Instruction> body;
};

class TemplateRegistry {
    void registerTemplate(Template&& tmpl);
    Template* findBestMatch(const std::string& name, const std::vector<Argument>& args);
};

// CPU abstraction
class CPUInterface {
    virtual std::vector<Instruction> getInstructionSet() = 0;
    virtual bool validateInstruction(const Instruction& instr) = 0;
    virtual std::string generateCode(const Instruction& instr) = 0;
};

// AST nodes
class ASTNode { /* base class */ };
class TemplateDefinition : public ASTNode { /* ... */ };
class TemplateInstantiation : public ASTNode { /* ... */ };
class AssemblyInstruction : public ASTNode { /* ... */ };
```

## Template System Design

### Template Definition
```nex
template boeq(var1, var2) address {
    lda var1
    cmp var2
    beq address
}
```

### Template Overloading
```nex
template boeq(var1, var2=A) address {
    cmp var1      // var2 defaults to A register
    beq address
}
```

### Parameter Types
- **Variables**: Memory locations or labels
- **Registers**: CPU registers (A, X, Y for 6502)
- **Immediates**: Literal values
- **Addresses**: Jump/branch targets
- **Default Values**: Optional parameters with defaults

## Compilation Pipeline

1. **Lexical Analysis**: Source → Tokens
2. **Parsing**: Tokens → AST
3. **Template Registration**: Collect template definitions
4. **Template Expansion**: Instantiate templates in AST
5. **Optimization**: Compile-time optimizations
6. **Code Generation**: AST → Target Assembly
7. **Output**: Final assembly file

## Error Handling Strategy

- **Lexical Errors**: Invalid characters, unterminated strings
- **Syntax Errors**: Malformed template definitions, invalid assembly
- **Template Errors**: Unresolved templates, parameter mismatches
- **Semantic Errors**: Invalid instructions for target CPU

## Extensibility Points

1. **New CPU Support**: Implement CPUInterface
2. **Custom Instructions**: Extend instruction set definitions
3. **New Template Features**: Extend template parameter types
4. **Optimization Passes**: Add new compile-time optimizations

## Performance Considerations

- **Compile-time Expansion**: All templates resolved at compile time
- **Zero Runtime Overhead**: No runtime template processing
- **Efficient Symbol Tables**: Fast template and symbol lookup
- **Memory Management**: Smart pointers and RAII throughout