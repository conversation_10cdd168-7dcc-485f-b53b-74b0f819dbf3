/**
 * @file library_demo.nex
 * @brief Comprehensive demonstration of NEX library system
 *
 * This example showcases the power of NEX's import system and
 * standard library templates for creating complex programs.
 */

// Import standard libraries
import "standard.nex"           // Core templates
import "6502/6502.nex"         // 6502-specific optimizations
import "math/arithmetic.nex"   // Extended math operations

// Program constants
.org $8000

// =============================================================================
// MAIN PROGRAM
// =============================================================================

start:
    // Initialize system using standard library
    clear_registers()
    clear_memory(counter)
    clear_memory(result)

    // Demonstrate basic arithmetic using standard templates
    load_immediate(A, #10)
    store_register(A, num1)

    load_immediate(A, #5)
    store_register(A, num2)

    // Add two numbers using standard library
    add_variables(num1, num2, result)

    // Demonstrate conditional logic
    if_condition(result, #15) correct_result

    // If result is wrong, set error flag
    load_immediate(A, #$FF)
    store_register(A, error_flag)
    jmp main_loop

correct_result:
    // Result is correct, clear error flag
    clear_memory(error_flag)

main_loop:
    // Demonstrate loop using standard templates
    increment(counter)

    // Use 6502-specific optimization for zero page
    zp_copy(counter, $80)

    // Check loop condition
    branch_if_less(counter, #20) continue_loop
    jmp math_demo

continue_loop:
    // Demonstrate register operations
    save_registers()

    // Use 6502-specific templates
    load_immediate(A, counter)
    push_memory(counter)

    restore_registers()
    jmp main_loop

math_demo:
    // Demonstrate 16-bit arithmetic
    load_immediate(A, #$34)
    store_register(A, big_num_lo)
    load_immediate(A, #$12)
    store_register(A, big_num_hi)

    load_immediate(A, #$CD)
    store_register(A, big_num2_lo)
    load_immediate(A, #$AB)
    store_register(A, big_num2_hi)

    // Add 16-bit numbers: $1234 + $ABCD
    add_16bit(big_num_lo, big_num_hi, big_num2_lo, big_num2_hi, sum_lo, sum_hi)

    // Demonstrate multiplication
    load_immediate(A, #12)
    store_register(A, multiplicand)
    load_immediate(A, #7)
    store_register(A, multiplier)

    multiply_8bit(multiplicand, multiplier, product_lo, product_hi)

    // Fast power-of-2 operations
    load_immediate(A, #15)
    store_register(A, test_value)

    multiply_by_4(test_value, quadrupled)
    divide_by_2(quadrupled, halved)

optimization_demo:
    // Demonstrate 6502-specific optimizations

    // Fast zero page operations
    zp_clear($81)
    zp_clear($82)

    // Set up indirect addressing
    load_immediate(A, #<data_table)
    store_register(A, $83)
    load_immediate(A, #>data_table)
    store_register(A, $84)

    // Use indirect addressing to access data
    load_indirect($83, #0)
    store_register(A, indirect_result)

    // Demonstrate bit testing
    load_immediate(A, #%10000000)
    store_register(A, test_bits)

    test_bit_7(test_bits) bit_set
    jmp bit_clear

bit_set:
    load_immediate(A, #1)
    store_register(A, bit_result)
    jmp utility_demo

bit_clear:
    clear_memory(bit_result)

utility_demo:
    // Demonstrate utility templates

    // Swap two variables
    load_immediate(A, #$AA)
    store_register(A, swap1)
    load_immediate(A, #$55)
    store_register(A, swap2)

    swap_variables(swap1, swap2)

    // Create a delay
    delay(10)

    // Demonstrate counting loop
    clear_memory(loop_counter)

counting_loop_start:
    // Loop body - increment a value
    increment(loop_result)

    // Use counting loop template
    counting_loop(loop_counter, #5) counting_loop_start, program_end

program_end:
    // Program completed successfully
    load_immediate(A, #$00)
    store_register(A, exit_code)

    brk

// =============================================================================
// DATA SECTION
// =============================================================================

// Basic variables
counter:        .byte $00
result:         .byte $00
num1:           .byte $00
num2:           .byte $00
error_flag:     .byte $00

// 16-bit arithmetic variables
big_num_lo:     .byte $00
big_num_hi:     .byte $00
big_num2_lo:    .byte $00
big_num2_hi:    .byte $00
sum_lo:         .byte $00
sum_hi:         .byte $00

// Multiplication variables
multiplicand:   .byte $00
multiplier:     .byte $00
product_lo:     .byte $00
product_hi:     .byte $00

// Test variables
test_value:     .byte $00
quadrupled:     .byte $00
halved:         .byte $00

// Optimization demo variables
indirect_result: .byte $00
test_bits:      .byte $00
bit_result:     .byte $00

// Utility demo variables
swap1:          .byte $00
swap2:          .byte $00
loop_counter:   .byte $00
loop_result:    .byte $00
exit_code:      .byte $00

// Data table for indirect addressing
data_table:
    .byte $01, $02, $03, $04, $05
    .byte $06, $07, $08, $09, $0A

// =============================================================================
// RESET VECTOR
// =============================================================================

.org $FFFC
.word start