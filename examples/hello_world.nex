; NEX Example: Hello World with Template Metaprogramming
; This demonstrates the template system for 6502 assembly

; Template definition: Branch on Equal
template boeq(var1, var2) address {
    lda var1
    cmp var2
    beq address
}

; Template overload: Branch on Equal with A register default
template boeq(var1, var2=A) address {
    cmp var1      ; assumes var2 is already in A
    beq address
}

; Template: Load immediate value
template ldi(register, value) {
    lda #value
}

; Template: Store to memory location
template store(register, location) {
    sta location
}

; Template: Simple loop counter
template loop_counter(counter, limit) loop_start, loop_end {
    lda counter
    cmp #limit
    bcs loop_end
    inc counter
    jmp loop_start
}

; Main program starts here
    .org $8000

start:
    ; Initialize system
    ldi(A, $00)
    store(A, $0200)

    ; Example usage of templates
    lda #$05
    sta counter

main_loop:
    ; Use template with default parameter
    boeq(counter) done

    ; Use full template
    boeq(counter, #$10) done

    ; Use loop counter template
    loop_counter(counter, $10) main_loop, done

done:
    ; Program end
    brk

; Data section
counter: .byte $00
message: .text "Hello, NEX World!"

; Reset vector
    .org $FFFC
    .word start