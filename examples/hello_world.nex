// NEX Example: Hello World with Template Metaprogramming and Standard Library
// comments are marked with // instead of ;

// Import standard library templates
// Core standard library specific to the target CPU
import "standard/6502.nex"

// Overall syntax resembles C or C++, with a few exceptions to make it more flexible
// ; at the end of a line is optional

// Create variable globally
u8  myValues1;
u16 anAddress1;

// Create arrays
u8  myArray1[10];
u16 myArray2[10];

// Address can be specified 
u8($20) myData1;
u16($0300) myData2;

// The first addresses of the ZP are automatically assigned to temporaries
// These are used by variable creared locally, inside functions
// If you force the use of a temporary address, the compiler will generate a warning.
// If you try to use an address that is already used, the compiler will generate an error.

// Code starts at the start keyword. It's like the main in C. It's also a label.
// Optional parameter can be passed indicating the location in the final rom.
// Default address is $8000

set reset main;

main($8000) {

	// Initialize system using standard library templates
	//
	ClearRegisters();
	ClearMemory();

	// attribution means load and store
	myValues1 = 5; // lda #5 sta myValues1
	
	// the templates in the standard need to have the proper overload to account for different modes
	myData1 = myValues1; // lda myValues1 sta myData1
	
	// ampersand & is used to get the address
	anAddress1 = &myData1; // lda #<myData1 sta anAddress1 lda #>myData1 sta anAddress1+1

	// loops explicitly use the X or Y register.
	// This starts at 0 and goes to 99, looping 100 times
	
	forx(100) {
		myArray1[x] = x;
	}



}