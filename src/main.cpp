/**
 * @file main.cpp
 * @brief NEX Compiler - Template-based Macro Assembler
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <memory>

// NEX compiler components (will be implemented)
// #include "nex/lexer/tokenizer.h"
// #include "nex/parser/parser.h"
// #include "nex/templates/template_registry.h"
// #include "nex/codegen/code_generator.h"
// #include "nex/cpu/cpu_6502.h"

void printUsage(const std::string& programName) {
    std::cout << "NEX Compiler v1.0.0 - Template-based Macro Assembler\n";
    std::cout << "Usage: " << programName << " [options] <input.nex>\n";
    std::cout << "\nOptions:\n";
    std::cout << "  -o <output>    Specify output file (default: input.asm)\n";
    std::cout << "  -t <target>    Target CPU (6502, z80) (default: 6502)\n";
    std::cout << "  -O <level>     Optimization level (0-3) (default: 2)\n";
    std::cout << "  -v, --verbose  Enable verbose output\n";
    std::cout << "  -h, --help     Show this help message\n";
    std::cout << "\nExample:\n";
    std::cout << "  " << programName << " -o program.asm -t 6502 program.nex\n";
}

struct CompilerOptions {
    std::string inputFile;
    std::string outputFile;
    std::string targetCPU = "6502";
    int optimizationLevel = 2;
    bool verbose = false;
};

bool parseArguments(int argc, char* argv[], CompilerOptions& options) {
    if (argc < 2) {
        return false;
    }

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help") {
            return false;
        } else if (arg == "-v" || arg == "--verbose") {
            options.verbose = true;
        } else if (arg == "-o" && i + 1 < argc) {
            options.outputFile = argv[++i];
        } else if (arg == "-t" && i + 1 < argc) {
            options.targetCPU = argv[++i];
        } else if (arg == "-O" && i + 1 < argc) {
            options.optimizationLevel = std::stoi(argv[++i]);
        } else if (arg[0] != '-') {
            options.inputFile = arg;
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            return false;
        }
    }

    if (options.inputFile.empty()) {
        std::cerr << "Error: No input file specified" << std::endl;
        return false;
    }

    // Set default output file if not specified
    if (options.outputFile.empty()) {
        size_t lastDot = options.inputFile.find_last_of('.');
        if (lastDot != std::string::npos) {
            options.outputFile = options.inputFile.substr(0, lastDot) + ".asm";
        } else {
            options.outputFile = options.inputFile + ".asm";
        }
    }

    return true;
}

int main(int argc, char* argv[]) {
    CompilerOptions options;

    if (!parseArguments(argc, argv, options)) {
        printUsage(argv[0]);
        return 1;
    }

    if (options.verbose) {
        std::cout << "NEX Compiler v1.0.0\n";
        std::cout << "Input file: " << options.inputFile << "\n";
        std::cout << "Output file: " << options.outputFile << "\n";
        std::cout << "Target CPU: " << options.targetCPU << "\n";
        std::cout << "Optimization level: " << options.optimizationLevel << "\n";
    }

    try {
        // Check if input file exists
        std::ifstream inputFile(options.inputFile);
        if (!inputFile.is_open()) {
            std::cerr << "Error: Cannot open input file: " << options.inputFile << std::endl;
            return 1;
        }

        // TODO: Implement the actual compilation pipeline
        std::cout << "NEX Compiler is under development.\n";
        std::cout << "The compilation pipeline will be implemented in the following order:\n";
        std::cout << "1. Lexical Analysis\n";
        std::cout << "2. Syntax Analysis\n";
        std::cout << "3. Template Registration\n";
        std::cout << "4. Template Expansion\n";
        std::cout << "5. Code Generation\n";
        std::cout << "6. Optimization\n";
        std::cout << "7. Output Generation\n";

        // For now, just copy the input to output with a comment
        std::ofstream outputFile(options.outputFile);
        if (!outputFile.is_open()) {
            std::cerr << "Error: Cannot create output file: " << options.outputFile << std::endl;
            return 1;
        }

        outputFile << "; Generated by NEX Compiler v1.0.0\n";
        outputFile << "; Source: " << options.inputFile << "\n";
        outputFile << "; Target: " << options.targetCPU << "\n";
        outputFile << "; Optimization: O" << options.optimizationLevel << "\n";
        outputFile << ";\n";
        outputFile << "; TODO: Implement actual compilation\n";
        outputFile << "\n";

        std::string line;
        while (std::getline(inputFile, line)) {
            outputFile << "; " << line << "\n";
        }

        if (options.verbose) {
            std::cout << "Compilation completed successfully.\n";
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}