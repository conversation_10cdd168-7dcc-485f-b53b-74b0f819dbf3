/**
 * @file tokenizer.cpp
 * @brief Tokenizer implementation for NEX lexical analysis
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

#include "nex/lexer/tokenizer.h"
#include <sstream>
#include <cctype>
#include <algorithm>

namespace nex::lexer {

// Static lookup tables
const std::unordered_map<std::string, TokenType> Tokenizer::keywords_ = {
    {"template", TokenType::TEMPLATE},
    {"import", TokenType::IMPORT},
    {"set", TokenType::SET},
    {"reset", TokenType::RESET},
    {"main", TokenType::MAIN},
    {"forx", TokenType::FORX},
    {"fory", TokenType::FORY},
    {"u8", TokenType::U8},
    {"u16", TokenType::U16}
};

const std::unordered_map<std::string, TokenType> Tokenizer::instructions_ = {
    // Load/Store
    {"lda", TokenType::LDA}, {"ldx", TokenType::LDX}, {"ldy", TokenType::LDY},
    {"sta", TokenType::STA}, {"stx", TokenType::STX}, {"sty", TokenType::STY},

    // Arithmetic
    {"adc", TokenType::ADC}, {"sbc", TokenType::SBC},
    {"inc", TokenType::INC}, {"dec", TokenType::DEC},
    {"inx", TokenType::INX}, {"dex", TokenType::DEX},
    {"iny", TokenType::INY}, {"dey", TokenType::DEY},

    // Logic
    {"and", TokenType::AND}, {"ora", TokenType::ORA}, {"eor", TokenType::EOR},

    // Compare
    {"cmp", TokenType::CMP}, {"cpx", TokenType::CPX}, {"cpy", TokenType::CPY},

    // Branches
    {"beq", TokenType::BEQ}, {"bne", TokenType::BNE},
    {"bcs", TokenType::BCS}, {"bcc", TokenType::BCC},
    {"bmi", TokenType::BMI}, {"bpl", TokenType::BPL},
    {"bvs", TokenType::BVS}, {"bvc", TokenType::BVC},

    // Jumps
    {"jmp", TokenType::JMP}, {"jsr", TokenType::JSR},
    {"rts", TokenType::RTS}, {"rti", TokenType::RTI},

    // Stack
    {"pha", TokenType::PHA}, {"pla", TokenType::PLA},
    {"php", TokenType::PHP}, {"plp", TokenType::PLP},

    // Transfer
    {"tax", TokenType::TAX}, {"tay", TokenType::TAY},
    {"txa", TokenType::TXA}, {"tya", TokenType::TYA},
    {"tsx", TokenType::TSX}, {"txs", TokenType::TXS},

    // Status
    {"clc", TokenType::CLC}, {"sec", TokenType::SEC},
    {"cli", TokenType::CLI}, {"sei", TokenType::SEI},
    {"clv", TokenType::CLV}, {"cld", TokenType::CLD}, {"sed", TokenType::SED},

    // System
    {"brk", TokenType::BRK}, {"nop", TokenType::NOP}
};

const std::unordered_map<std::string, TokenType> Tokenizer::directives_ = {
    {"org", TokenType::ORG},
    {"byte", TokenType::BYTE},
    {"word", TokenType::WORD},
    {"text", TokenType::TEXT}
};

const std::unordered_map<std::string, TokenType> Tokenizer::registers_ = {
    {"A", TokenType::REGISTER},
    {"X", TokenType::REGISTER},
    {"Y", TokenType::REGISTER}
};

Tokenizer::Tokenizer(const std::string& source, const std::string& filename)
    : source_(source), filename_(filename), position_(0), line_(1), column_(1) {
}

Tokenizer::Tokenizer(std::istream& input, const std::string& filename)
    : filename_(filename), position_(0), line_(1), column_(1) {
    std::ostringstream buffer;
    buffer << input.rdbuf();
    source_ = buffer.str();
}

Token Tokenizer::nextToken() {
    skipWhitespace();

    if (position_ >= source_.length()) {
        return Token(TokenType::END_OF_FILE, std::monostate{}, makeLocation());
    }

    char current = currentChar();
    SourceLocation location = makeLocation();

    // Single character tokens
    switch (current) {
        case '(':
            advance();
            return Token(TokenType::LEFT_PAREN, std::monostate{}, location);
        case ')':
            advance();
            return Token(TokenType::RIGHT_PAREN, std::monostate{}, location);
        case '{':
            advance();
            return Token(TokenType::LEFT_BRACE, std::monostate{}, location);
        case '}':
            advance();
            return Token(TokenType::RIGHT_BRACE, std::monostate{}, location);
        case ',':
            advance();
            return Token(TokenType::COMMA, std::monostate{}, location);
        case '=':
            advance();
            return Token(TokenType::ASSIGN, std::monostate{}, location);
        case '#':
            advance();
            return Token(TokenType::HASH, std::monostate{}, location);
        case '$':
            advance();
            return Token(TokenType::DOLLAR, std::monostate{}, location);
        case '%':
            advance();
            return Token(TokenType::PERCENT, std::monostate{}, location);
        case ':':
            advance();
            return Token(TokenType::COLON, std::monostate{}, location);
        case ';':
            skipComment();
            return nextToken(); // Skip comment and get next token
        case '&':
            advance();
            return Token(TokenType::AMPERSAND, std::monostate{}, location);
        case '[':
            advance();
            return Token(TokenType::LEFT_BRACKET, std::monostate{}, location);
        case ']':
            advance();
            return Token(TokenType::RIGHT_BRACKET, std::monostate{}, location);
        case '/':
            if (peekChar() == '/') {
                advance(); // Skip first /
                advance(); // Skip second /
                skipCppComment();
                return nextToken(); // Skip comment and get next token
            }
            break;
        case '\n':
            advance();
            return Token(TokenType::NEWLINE, std::monostate{}, location);
    }

    // Multi-character tokens
    if (current == '.') {
        return readDirective();
    }

    if (current == '"') {
        return readString();
    }

    if (isDigit(current)) {
        return readNumber();
    }

    if (isAlpha(current) || current == '_') {
        return readIdentifier();
    }

    // Invalid character
    addError("Invalid character: " + std::string(1, current));
    advance();
    return Token(TokenType::INVALID, std::string(1, current), location);
}

Token Tokenizer::peekToken() {
    size_t saved_position = position_;
    size_t saved_line = line_;
    size_t saved_column = column_;

    Token token = nextToken();

    position_ = saved_position;
    line_ = saved_line;
    column_ = saved_column;

    return token;
}

bool Tokenizer::hasMoreTokens() const {
    return position_ < source_.length();
}

std::vector<Token> Tokenizer::tokenizeAll() {
    std::vector<Token> tokens;

    while (hasMoreTokens()) {
        Token token = nextToken();
        if (token.type == TokenType::END_OF_FILE) {
            tokens.push_back(token);
            break;
        }
        tokens.push_back(token);
    }

    return tokens;
}

SourceLocation Tokenizer::getCurrentLocation() const {
    return makeLocation();
}

char Tokenizer::currentChar() const {
    if (position_ >= source_.length()) {
        return '\0';
    }
    return source_[position_];
}

char Tokenizer::peekChar(size_t offset) const {
    size_t peek_pos = position_ + offset;
    if (peek_pos >= source_.length()) {
        return '\0';
    }
    return source_[peek_pos];
}

void Tokenizer::advance() {
    if (position_ < source_.length()) {
        if (source_[position_] == '\n') {
            line_++;
            column_ = 1;
        } else {
            column_++;
        }
        position_++;
    }
}

void Tokenizer::skipWhitespace() {
    while (position_ < source_.length() && isWhitespace(currentChar()) && currentChar() != '\n') {
        advance();
    }
}

void Tokenizer::skipComment() {
    // Skip ';' character
    advance();

    // Skip until end of line
    while (position_ < source_.length() && currentChar() != '\n') {
        advance();
    }
}

void Tokenizer::skipCppComment() {
    // Skip until end of line (// already consumed)
    while (position_ < source_.length() && currentChar() != '\n') {
        advance();
    }
}

Token Tokenizer::readIdentifier() {
    SourceLocation location = makeLocation();
    std::string identifier;

    while (position_ < source_.length() && (isAlphaNumeric(currentChar()) || currentChar() == '_')) {
        identifier += currentChar();
        advance();
    }

    // Convert to lowercase for lookup
    std::string lower_identifier = identifier;
    std::transform(lower_identifier.begin(), lower_identifier.end(), lower_identifier.begin(), ::tolower);

    // Check if it's a keyword
    auto keyword_it = keywords_.find(lower_identifier);
    if (keyword_it != keywords_.end()) {
        return Token(keyword_it->second, identifier, location);
    }

    // Check if it's an instruction
    auto instruction_it = instructions_.find(lower_identifier);
    if (instruction_it != instructions_.end()) {
        return Token(instruction_it->second, identifier, location);
    }

    // Check if it's a register
    auto register_it = registers_.find(identifier); // Registers are case-sensitive
    if (register_it != registers_.end()) {
        return Token(register_it->second, identifier, location);
    }

    // It's a regular identifier
    return Token(TokenType::IDENTIFIER, identifier, location);
}

Token Tokenizer::readNumber() {
    SourceLocation location = makeLocation();

    // Check for hex prefix ($)
    if (currentChar() == '$') {
        advance();
        std::string hex_str;
        while (position_ < source_.length() && isHexDigit(currentChar())) {
            hex_str += currentChar();
            advance();
        }
        if (hex_str.empty()) {
            addError("Invalid hexadecimal number");
            return Token(TokenType::INVALID, std::monostate{}, location);
        }
        return Token(TokenType::INTEGER_LITERAL, parseHexadecimal(hex_str), location);
    }

    // Check for binary prefix (%)
    if (currentChar() == '%') {
        advance();
        std::string bin_str;
        while (position_ < source_.length() && isBinaryDigit(currentChar())) {
            bin_str += currentChar();
            advance();
        }
        if (bin_str.empty()) {
            addError("Invalid binary number");
            return Token(TokenType::INVALID, std::monostate{}, location);
        }
        return Token(TokenType::INTEGER_LITERAL, parseBinary(bin_str), location);
    }

    // Regular decimal number
    std::string dec_str;
    while (position_ < source_.length() && isDigit(currentChar())) {
        dec_str += currentChar();
        advance();
    }

    return Token(TokenType::INTEGER_LITERAL, parseDecimal(dec_str), location);
}

Token Tokenizer::readString() {
    SourceLocation location = makeLocation();
    advance(); // Skip opening quote

    std::string str_value;
    while (position_ < source_.length() && currentChar() != '"') {
        if (currentChar() == '\\') {
            advance();
            if (position_ >= source_.length()) {
                addError("Unterminated string literal");
                return Token(TokenType::INVALID, std::monostate{}, location);
            }

            // Handle escape sequences
            switch (currentChar()) {
                case 'n': str_value += '\n'; break;
                case 't': str_value += '\t'; break;
                case 'r': str_value += '\r'; break;
                case '\\': str_value += '\\'; break;
                case '"': str_value += '"'; break;
                default:
                    str_value += currentChar();
                    break;
            }
        } else {
            str_value += currentChar();
        }
        advance();
    }

    if (position_ >= source_.length()) {
        addError("Unterminated string literal");
        return Token(TokenType::INVALID, std::monostate{}, location);
    }

    advance(); // Skip closing quote
    return Token(TokenType::STRING_LITERAL, str_value, location);
}

Token Tokenizer::readDirective() {
    SourceLocation location = makeLocation();
    advance(); // Skip '.'

    std::string directive;
    while (position_ < source_.length() && isAlphaNumeric(currentChar())) {
        directive += currentChar();
        advance();
    }

    // Convert to lowercase for lookup
    std::string lower_directive = directive;
    std::transform(lower_directive.begin(), lower_directive.end(), lower_directive.begin(), ::tolower);

    auto directive_it = directives_.find(lower_directive);
    if (directive_it != directives_.end()) {
        return Token(directive_it->second, directive, location);
    }

    // Unknown directive, treat as identifier with dot
    return Token(TokenType::IDENTIFIER, "." + directive, location);
}

bool Tokenizer::isAlpha(char c) const {
    return std::isalpha(c) || c == '_';
}

bool Tokenizer::isDigit(char c) const {
    return std::isdigit(c);
}

bool Tokenizer::isAlphaNumeric(char c) const {
    return isAlpha(c) || isDigit(c);
}

bool Tokenizer::isHexDigit(char c) const {
    return std::isxdigit(c);
}

bool Tokenizer::isBinaryDigit(char c) const {
    return c == '0' || c == '1';
}

bool Tokenizer::isWhitespace(char c) const {
    return c == ' ' || c == '\t' || c == '\r';
}

void Tokenizer::addError(const std::string& message) {
    std::ostringstream oss;
    oss << filename_ << ":" << line_ << ":" << column_ << ": " << message;
    errors_.push_back(oss.str());
}

SourceLocation Tokenizer::makeLocation() const {
    return SourceLocation(filename_, line_, column_);
}

int64_t Tokenizer::parseDecimal(const std::string& str) {
    return std::stoll(str, nullptr, 10);
}

int64_t Tokenizer::parseHexadecimal(const std::string& str) {
    return std::stoll(str, nullptr, 16);
}

int64_t Tokenizer::parseBinary(const std::string& str) {
    return std::stoll(str, nullptr, 2);
}

} // namespace nex::lexer