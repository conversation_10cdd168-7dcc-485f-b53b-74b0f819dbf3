/**
 * @file token.cpp
 * @brief Token implementation for NEX lexical analysis
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

#include "nex/lexer/token.h"
#include <unordered_map>
#include <sstream>

namespace nex::lexer {

bool Token::isKeyword() const {
    return type == TokenType::TEMPLATE || type == TokenType::IMPORT ||
           type == TokenType::SET || type == TokenType::RESET ||
           type == TokenType::MAIN || type == TokenType::FORX ||
           type == TokenType::FORY || type == TokenType::U8 ||
           type == TokenType::U16;
}

bool Token::isInstruction() const {
    return (type >= TokenType::LDA && type <= TokenType::NOP);
}

bool Token::isLiteral() const {
    return type == TokenType::IDENTIFIER ||
           type == TokenType::INTEGER_LITERAL ||
           type == TokenType::STRING_LITERAL ||
           type == TokenType::REGISTER;
}

bool Token::isOperator() const {
    return (type >= TokenType::LEFT_PAREN && type <= TokenType::SEMICOLON);
}

bool Token::isDirective() const {
    return (type >= TokenType::ORG && type <= TokenType::TEXT);
}

std::string Token::toString() const {
    std::ostringstream oss;
    oss << tokenTypeToString(type);

    if (std::holds_alternative<std::string>(value)) {
        oss << "(\"" << std::get<std::string>(value) << "\")";
    } else if (std::holds_alternative<int64_t>(value)) {
        oss << "(" << std::get<int64_t>(value) << ")";
    }

    oss << " at " << location.filename << ":" << location.line << ":" << location.column;
    return oss.str();
}

std::string Token::getStringValue() const {
    if (std::holds_alternative<std::string>(value)) {
        return std::get<std::string>(value);
    }
    return "";
}

int64_t Token::getIntValue() const {
    if (std::holds_alternative<int64_t>(value)) {
        return std::get<int64_t>(value);
    }
    return 0;
}

std::string tokenTypeToString(TokenType type) {
    static const std::unordered_map<TokenType, std::string> tokenNames = {
        {TokenType::END_OF_FILE, "END_OF_FILE"},
        {TokenType::IDENTIFIER, "IDENTIFIER"},
        {TokenType::INTEGER_LITERAL, "INTEGER_LITERAL"},
        {TokenType::STRING_LITERAL, "STRING_LITERAL"},
        {TokenType::REGISTER, "REGISTER"},
        {TokenType::TEMPLATE, "TEMPLATE"},
        {TokenType::IMPORT, "IMPORT"},
        {TokenType::SET, "SET"},
        {TokenType::RESET, "RESET"},
        {TokenType::MAIN, "MAIN"},
        {TokenType::FORX, "FORX"},
        {TokenType::FORY, "FORY"},
        {TokenType::U8, "U8"},
        {TokenType::U16, "U16"},
        {TokenType::LEFT_PAREN, "LEFT_PAREN"},
        {TokenType::RIGHT_PAREN, "RIGHT_PAREN"},
        {TokenType::LEFT_BRACE, "LEFT_BRACE"},
        {TokenType::RIGHT_BRACE, "RIGHT_BRACE"},
        {TokenType::COMMA, "COMMA"},
        {TokenType::EQUALS, "EQUALS"},
        {TokenType::HASH, "HASH"},
        {TokenType::DOLLAR, "DOLLAR"},
        {TokenType::PERCENT, "PERCENT"},
        {TokenType::DOT, "DOT"},
        {TokenType::COLON, "COLON"},
        {TokenType::SEMICOLON, "SEMICOLON"},
        {TokenType::AMPERSAND, "AMPERSAND"},
        {TokenType::ASSIGN, "ASSIGN"},
        {TokenType::LEFT_BRACKET, "LEFT_BRACKET"},
        {TokenType::RIGHT_BRACKET, "RIGHT_BRACKET"},
        {TokenType::DOUBLE_SLASH, "DOUBLE_SLASH"},
        {TokenType::LDA, "LDA"},
        {TokenType::LDX, "LDX"},
        {TokenType::LDY, "LDY"},
        {TokenType::STA, "STA"},
        {TokenType::STX, "STX"},
        {TokenType::STY, "STY"},
        {TokenType::ADC, "ADC"},
        {TokenType::SBC, "SBC"},
        {TokenType::INC, "INC"},
        {TokenType::DEC, "DEC"},
        {TokenType::INX, "INX"},
        {TokenType::DEX, "DEX"},
        {TokenType::INY, "INY"},
        {TokenType::DEY, "DEY"},
        {TokenType::AND, "AND"},
        {TokenType::ORA, "ORA"},
        {TokenType::EOR, "EOR"},
        {TokenType::CMP, "CMP"},
        {TokenType::CPX, "CPX"},
        {TokenType::CPY, "CPY"},
        {TokenType::BEQ, "BEQ"},
        {TokenType::BNE, "BNE"},
        {TokenType::BCS, "BCS"},
        {TokenType::BCC, "BCC"},
        {TokenType::BMI, "BMI"},
        {TokenType::BPL, "BPL"},
        {TokenType::BVS, "BVS"},
        {TokenType::BVC, "BVC"},
        {TokenType::JMP, "JMP"},
        {TokenType::JSR, "JSR"},
        {TokenType::RTS, "RTS"},
        {TokenType::RTI, "RTI"},
        {TokenType::PHA, "PHA"},
        {TokenType::PLA, "PLA"},
        {TokenType::PHP, "PHP"},
        {TokenType::PLP, "PLP"},
        {TokenType::TAX, "TAX"},
        {TokenType::TAY, "TAY"},
        {TokenType::TXA, "TXA"},
        {TokenType::TYA, "TYA"},
        {TokenType::TSX, "TSX"},
        {TokenType::TXS, "TXS"},
        {TokenType::CLC, "CLC"},
        {TokenType::SEC, "SEC"},
        {TokenType::CLI, "CLI"},
        {TokenType::SEI, "SEI"},
        {TokenType::CLV, "CLV"},
        {TokenType::CLD, "CLD"},
        {TokenType::SED, "SED"},
        {TokenType::BRK, "BRK"},
        {TokenType::NOP, "NOP"},
        {TokenType::ORG, "ORG"},
        {TokenType::BYTE, "BYTE"},
        {TokenType::WORD, "WORD"},
        {TokenType::TEXT, "TEXT"},
        {TokenType::NEWLINE, "NEWLINE"},
        {TokenType::WHITESPACE, "WHITESPACE"},
        {TokenType::COMMENT, "COMMENT"},
        {TokenType::INVALID, "INVALID"}
    };

    auto it = tokenNames.find(type);
    return (it != tokenNames.end()) ? it->second : "UNKNOWN";
}

} // namespace nex::lexer