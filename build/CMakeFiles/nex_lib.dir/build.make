# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Research/NEX

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Research/NEX/build

# Include any dependencies generated for this target.
include CMakeFiles/nex_lib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/nex_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/nex_lib.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/nex_lib.dir/flags.make

CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o: CMakeFiles/nex_lib.dir/flags.make
CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o: /home/<USER>/Research/NEX/src/lexer/tokenizer.cpp
CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o: CMakeFiles/nex_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Research/NEX/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o -MF CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o.d -o CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o -c /home/<USER>/Research/NEX/src/lexer/tokenizer.cpp

CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Research/NEX/src/lexer/tokenizer.cpp > CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.i

CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Research/NEX/src/lexer/tokenizer.cpp -o CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.s

CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o: CMakeFiles/nex_lib.dir/flags.make
CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o: /home/<USER>/Research/NEX/src/lexer/token.cpp
CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o: CMakeFiles/nex_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Research/NEX/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o -MF CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o.d -o CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o -c /home/<USER>/Research/NEX/src/lexer/token.cpp

CMakeFiles/nex_lib.dir/src/lexer/token.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/nex_lib.dir/src/lexer/token.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Research/NEX/src/lexer/token.cpp > CMakeFiles/nex_lib.dir/src/lexer/token.cpp.i

CMakeFiles/nex_lib.dir/src/lexer/token.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/nex_lib.dir/src/lexer/token.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Research/NEX/src/lexer/token.cpp -o CMakeFiles/nex_lib.dir/src/lexer/token.cpp.s

# Object files for target nex_lib
nex_lib_OBJECTS = \
"CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o" \
"CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o"

# External object files for target nex_lib
nex_lib_EXTERNAL_OBJECTS =

libnex_lib.a: CMakeFiles/nex_lib.dir/src/lexer/tokenizer.cpp.o
libnex_lib.a: CMakeFiles/nex_lib.dir/src/lexer/token.cpp.o
libnex_lib.a: CMakeFiles/nex_lib.dir/build.make
libnex_lib.a: CMakeFiles/nex_lib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Research/NEX/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libnex_lib.a"
	$(CMAKE_COMMAND) -P CMakeFiles/nex_lib.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/nex_lib.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/nex_lib.dir/build: libnex_lib.a
.PHONY : CMakeFiles/nex_lib.dir/build

CMakeFiles/nex_lib.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/nex_lib.dir/cmake_clean.cmake
.PHONY : CMakeFiles/nex_lib.dir/clean

CMakeFiles/nex_lib.dir/depend:
	cd /home/<USER>/Research/NEX/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Research/NEX /home/<USER>/Research/NEX /home/<USER>/Research/NEX/build /home/<USER>/Research/NEX/build /home/<USER>/Research/NEX/build/CMakeFiles/nex_lib.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/nex_lib.dir/depend

