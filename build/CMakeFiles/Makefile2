# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Research/NEX

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Research/NEX/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/nex_lib.dir/all
all: CMakeFiles/nex.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/nex_lib.dir/clean
clean: CMakeFiles/nex.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/nex_lib.dir

# All Build rule for target.
CMakeFiles/nex_lib.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nex_lib.dir/build.make CMakeFiles/nex_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nex_lib.dir/build.make CMakeFiles/nex_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Research/NEX/build/CMakeFiles --progress-num=3,4,5 "Built target nex_lib"
.PHONY : CMakeFiles/nex_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/nex_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Research/NEX/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/nex_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Research/NEX/build/CMakeFiles 0
.PHONY : CMakeFiles/nex_lib.dir/rule

# Convenience name for target.
nex_lib: CMakeFiles/nex_lib.dir/rule
.PHONY : nex_lib

# clean rule for target.
CMakeFiles/nex_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nex_lib.dir/build.make CMakeFiles/nex_lib.dir/clean
.PHONY : CMakeFiles/nex_lib.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/nex.dir

# All Build rule for target.
CMakeFiles/nex.dir/all: CMakeFiles/nex_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nex.dir/build.make CMakeFiles/nex.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nex.dir/build.make CMakeFiles/nex.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Research/NEX/build/CMakeFiles --progress-num=1,2 "Built target nex"
.PHONY : CMakeFiles/nex.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/nex.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Research/NEX/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/nex.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Research/NEX/build/CMakeFiles 0
.PHONY : CMakeFiles/nex.dir/rule

# Convenience name for target.
nex: CMakeFiles/nex.dir/rule
.PHONY : nex

# clean rule for target.
CMakeFiles/nex.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nex.dir/build.make CMakeFiles/nex.dir/clean
.PHONY : CMakeFiles/nex.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

